import { Metadata } from "next";
import prisma from "@/lib/prisma";

// Get company info for metadata generation
export async function getCompanyInfo() {
  try {
    const companyInfo = await prisma.companyInfo.findFirst();
    
    if (!companyInfo) {
      // Return default values if no company info exists
      return {
        companyName: "Essay App",
        description: "Professional academic writing services for students at all levels. Get expert help with essays, research papers, dissertations, and more.",
        website: "https://essayscholars.us",
        supportEmail: "<EMAIL>",
      };
    }

    return {
      companyName: companyInfo.companyName,
      description: companyInfo.description || "Professional academic writing services for students at all levels. Get expert help with essays, research papers, dissertations, and more.",
      website: companyInfo.website || "https://essayscholars.us",
      supportEmail: companyInfo.supportEmail,
    };
  } catch (error) {
    console.error("Error fetching company info for metadata:", error);
    // Return default values on error
    return {
      companyName: "Essay App",
      description: "Professional academic writing services for students at all levels. Get expert help with essays, research papers, dissertations, and more.",
      website: "https://essayscholars.us",
      supportEmail: "<EMAIL>",
    };
  }
}

// Generate dynamic metadata with company info
export async function generateDynamicMetadata({
  title,
  description,
  keywords = [],
  path = "",
  ogImage,
}: {
  title: string;
  description: string;
  keywords?: string[];
  path?: string;
  ogImage?: string;
}): Promise<Metadata> {
  const companyInfo = await getCompanyInfo();
  const baseUrl = process.env.NEXTAUTH_URL || "https://homeworkassylum.com";
  const fullUrl = `${baseUrl}${path}`;
  const defaultOgImage = `${baseUrl}/opengraph-image.png`;

  return {
    title: `${title} | ${companyInfo.companyName}`,
    description,
    keywords,
    authors: [{ name: companyInfo.companyName }],
    creator: companyInfo.companyName,
    publisher: companyInfo.companyName,
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
    openGraph: {
      title: `${title} | ${companyInfo.companyName}`,
      description,
      url: fullUrl,
      siteName: companyInfo.companyName,
      images: [
        {
          url: ogImage || defaultOgImage,
          width: 1200,
          height: 630,
          alt: `${title} - ${companyInfo.companyName}`,
        },
      ],
      locale: "en_US",
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title: `${title} | ${companyInfo.companyName}`,
      description,
      creator: `@${companyInfo.companyName.toLowerCase().replace(/\s+/g, "")}`,
      images: [ogImage || defaultOgImage],
    },
    alternates: {
      canonical: fullUrl,
    },
  };
}

// Generate root layout metadata
export async function generateRootMetadata(): Promise<Metadata> {
  const companyInfo = await getCompanyInfo();
  const baseUrl = process.env.NEXTAUTH_URL || "https://homeworkassylum.com";

  return {
    title: {
      default: companyInfo.companyName,
      template: `%s | ${companyInfo.companyName}`,
    },
    description: companyInfo.description,
    keywords: [
      "academic writing",
      "essay help",
      "homework assistance",
      "research papers",
      "student support",
      "professional writing",
      "academic services",
    ],
    authors: [{ name: companyInfo.companyName }],
    creator: companyInfo.companyName,
    publisher: companyInfo.companyName,
    metadataBase: new URL(baseUrl),
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
    openGraph: {
      title: companyInfo.companyName,
      description: companyInfo.description,
      url: baseUrl,
      siteName: companyInfo.companyName,
      images: [
        {
          url: `${baseUrl}/opengraph-image.png`,
          width: 1200,
          height: 630,
          alt: `${companyInfo.companyName} - Academic Writing Services`,
        },
      ],
      locale: "en_US",
      type: "website",
    },
    twitter: {
      card: "summary_large_image",
      title: companyInfo.companyName,
      description: companyInfo.description,
      creator: `@${companyInfo.companyName.toLowerCase().replace(/\s+/g, "")}`,
      images: [`${baseUrl}/opengraph-image.png`],
    },
    verification: {
      google: "your-google-verification-code",
    },
    icons: {
      icon: "/favicon.ico",
      shortcut: "/favicon.ico",
      apple: "/apple-icon.png",
    },
  };
}
